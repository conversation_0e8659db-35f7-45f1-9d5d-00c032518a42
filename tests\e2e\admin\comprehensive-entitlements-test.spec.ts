import { test, expect } from '@playwright/test';
import { 
  loginAsStoreOwner, 
  loginAsPrivilegedPlus, 
  loginAsPrivileged, 
  loginAsMemberChomsky, 
  loginAsMemberAristotle, 
  cleanupAuth,
  TEST_USERS 
} from '../utils/auth-helpers';

/**
 * Comprehensive Entitlements Investigation Tests for BookTalks Buddy
 * Phase 1: Context Gathering - Investigate entitlements system for all 5 users
 * Following the testing strategy from comprehensive-testing-session-prompt.md
 */

// Helper function to extract entitlements from browser cache
async function extractEntitlements(page: any, userType: string) {
  const entitlementsData = await page.evaluate(async () => {
    const data = {
      user: null as any,
      entitlements: [] as any[],
      roles: [] as any[],
      sessionStorage: {} as any,
      localStorage: {} as any,
      error: null as string | null
    };
    
    try {
      // Check sessionStorage for entitlements cache
      const sessionKeys = Object.keys(sessionStorage);
      sessionKeys.forEach(key => {
        if (key.includes('entitlements') || key.includes('auth') || key.includes('user') || key.includes('role')) {
          try {
            const value = sessionStorage.getItem(key);
            data.sessionStorage[key] = value ? JSON.parse(value) : value;
          } catch {
            data.sessionStorage[key] = sessionStorage.getItem(key);
          }
        }
      });
      
      // Check localStorage for entitlements cache
      const localKeys = Object.keys(localStorage);
      localKeys.forEach(key => {
        if (key.includes('entitlements') || key.includes('auth') || key.includes('user') || key.includes('role')) {
          try {
            const value = localStorage.getItem(key);
            data.localStorage[key] = value ? JSON.parse(value) : value;
          } catch {
            data.localStorage[key] = localStorage.getItem(key);
          }
        }
      });
      
      // Try to access global auth state if available
      if ((window as any).authState) {
        data.user = (window as any).authState.user;
        data.entitlements = (window as any).authState.entitlements || [];
        data.roles = (window as any).authState.roles || [];
      }
      
    } catch (error: any) {
      data.error = error.message;
    }
    
    return data;
  });
  
  console.log(`\n🔍 ${userType.toUpperCase()} ENTITLEMENTS ANALYSIS:`);
  console.log('='.repeat(50));
  console.log('SessionStorage:', JSON.stringify(entitlementsData.sessionStorage, null, 2));
  console.log('LocalStorage:', JSON.stringify(entitlementsData.localStorage, null, 2));
  console.log('User Data:', entitlementsData.user);
  console.log('Entitlements:', entitlementsData.entitlements);
  console.log('Roles:', entitlementsData.roles);
  console.log('Error:', entitlementsData.error);
  
  return entitlementsData;
}

test.describe('Phase 1: Comprehensive Entitlements Investigation', () => {
  
  test.describe('Store Owner Entitlements Analysis', () => {
    test('should investigate store owner entitlements and admin access', async ({ page }) => {
      // Login as store owner
      const loginSuccess = await loginAsStoreOwner(page);
      
      if (!loginSuccess) {
        test.skip('Could not authenticate as store owner');
      }
      
      // Navigate to profile to establish session
      await page.goto('/profile');
      await page.waitForTimeout(2000);
      
      // Extract entitlements data
      const entitlementsData = await extractEntitlements(page, 'Store Owner');
      
      // Test admin panel access (may load slowly)
      console.log('\n🚪 Testing Admin Panel Access (may be slow):');
      await page.goto('/admin');
      
      // Wait longer for admin panel due to known slow loading
      await page.waitForTimeout(5000);
      
      const adminUrl = page.url();
      console.log('Current URL after /admin navigation:', adminUrl);
      
      if (adminUrl.includes('/admin')) {
        console.log('✅ Store Owner CAN access admin panel');
        
        // Check what admin features are visible
        const adminFeatures = await page.evaluate(() => {
          const features = [];
          
          // Check for admin navigation and features
          const navLinks = document.querySelectorAll('nav a, [role="navigation"] a, button, [role="button"]');
          navLinks.forEach(link => {
            if (link.textContent && link.textContent.trim()) {
              features.push({
                text: link.textContent.trim(),
                href: (link as HTMLAnchorElement).href || 'button',
                tagName: link.tagName
              });
            }
          });
          
          return features.slice(0, 20); // Limit output
        });
        
        console.log('Available Admin Features:', adminFeatures);
        
        // Test specific admin routes
        const adminRoutes = ['/admin/store-management', '/admin/analytics', '/admin/users'];
        for (const route of adminRoutes) {
          await page.goto(route);
          await page.waitForTimeout(2000);
          const routeUrl = page.url();
          console.log(`${route}: ${routeUrl.includes(route.split('/').pop()!) ? '✅ Accessible' : '❌ Blocked'}`);
        }
        
      } else if (adminUrl.includes('/unauthorized')) {
        console.log('❌ Store Owner redirected to UNAUTHORIZED page');
        console.log('Missing required entitlements for admin access');
        
      } else {
        console.log('❌ Store Owner redirected away from admin panel');
        console.log('Redirected to:', adminUrl);
      }
      
      await cleanupAuth(page);
    });
  });

  test.describe('Privileged+ User Entitlements Analysis', () => {
    test('should investigate privileged+ user entitlements', async ({ page }) => {
      const loginSuccess = await loginAsPrivilegedPlus(page);
      
      if (!loginSuccess) {
        test.skip('Could not authenticate as privileged+ user');
      }
      
      await page.goto('/profile');
      await page.waitForTimeout(2000);
      
      const entitlementsData = await extractEntitlements(page, 'Privileged+');
      
      // Test access to various routes
      const testRoutes = [
        { path: '/book-clubs', name: 'Book Clubs' },
        { path: '/profile', name: 'Profile' },
        { path: '/events', name: 'Events' },
        { path: '/admin', name: 'Admin Panel' }
      ];
      
      console.log('\n🚪 Testing Route Access:');
      for (const route of testRoutes) {
        await page.goto(route.path);
        await page.waitForTimeout(2000);
        const currentUrl = page.url();
        const hasAccess = currentUrl.includes(route.path.split('/').pop()!) || currentUrl === route.path;
        console.log(`${route.name}: ${hasAccess ? '✅ Accessible' : '❌ Blocked'} (${currentUrl})`);
      }
      
      await cleanupAuth(page);
    });
  });

  test.describe('Member User Entitlements Analysis', () => {
    test('should investigate member user (Chomsky) entitlements', async ({ page }) => {
      const loginSuccess = await loginAsMemberChomsky(page);
      
      if (!loginSuccess) {
        test.skip('Could not authenticate as member user (Chomsky)');
      }
      
      await page.goto('/profile');
      await page.waitForTimeout(2000);
      
      const entitlementsData = await extractEntitlements(page, 'Member (Chomsky)');
      
      // Test access to various routes
      const testRoutes = [
        { path: '/book-clubs', name: 'Book Clubs' },
        { path: '/profile', name: 'Profile' },
        { path: '/events', name: 'Events' },
        { path: '/admin', name: 'Admin Panel' }
      ];
      
      console.log('\n🚪 Testing Route Access:');
      for (const route of testRoutes) {
        await page.goto(route.path);
        await page.waitForTimeout(2000);
        const currentUrl = page.url();
        const hasAccess = currentUrl.includes(route.path.split('/').pop()!) || currentUrl === route.path;
        console.log(`${route.name}: ${hasAccess ? '✅ Accessible' : '❌ Blocked'} (${currentUrl})`);
      }
      
      await cleanupAuth(page);
    });
  });

  test.describe('System Analysis Summary', () => {
    test('should provide comprehensive analysis summary', async ({ page }) => {
      console.log('\n📊 COMPREHENSIVE ENTITLEMENTS ANALYSIS SUMMARY:');
      console.log('='.repeat(60));
      console.log('');
      console.log('Test Users Analyzed:');
      console.log('- Store Owner: <EMAIL> (admin panel access)');
      console.log('- Privileged+: <EMAIL> (premium features)');
      console.log('- Privileged: <EMAIL> (enhanced features)');
      console.log('- Member: <EMAIL> (basic access)');
      console.log('- Member: <EMAIL> (basic access)');
      console.log('');
      console.log('Key Findings Expected:');
      console.log('- Entitlements cache structure in browser storage');
      console.log('- Role-based access control implementation');
      console.log('- Admin panel accessibility by role');
      console.log('- Feature availability differences');
      console.log('');
      console.log('Next Steps: Proceed to Phase 2 Core Testing');
      
      // This test always passes - it's for documentation
      expect(true).toBe(true);
    });
  });
});
